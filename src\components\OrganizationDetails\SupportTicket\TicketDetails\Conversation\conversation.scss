.convesation-wrap {
  .conversation-text {
    font-family: var(--font-family-primary);
  }
}

.conversation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  // max-height: 600px;
  background-color: var(--color-white);
  border: 1px solid var(--border-color-light-gray);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  margin-top: var(--spacing-xl);

  .conversation-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md)
      var(--spacing-lg);
    scroll-behavior: smooth;
    background-color: var(--color-light-gray-bg, #f8f9fa);

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-dark-20, #e0e0e0);
      border-radius: 3px;

      &:hover {
        background: var(--color-dark-30, #d0d0d0);
      }
    }

    .message-wrapper {
      margin-bottom: var(--spacing-lg);
      display: flex;
      width: 100%;
      animation: fadeInUp 0.3s ease-out;

      &.sent-message {
        justify-content: flex-end;

        .message-bubble {
          max-width: 75%;
          margin-left: auto;
          margin-right: 12px; // Space for arrow with border
          @media (max-width: 575px) {
            max-width: 90%;
          }
        }

        &.internal-note-wrapper {
          justify-content: flex-end;

          .message-bubble {
            max-width: 80%;
            margin-left: auto;
            margin-right: 12px; // Space for arrow with border
            @media (max-width: 575px) {
              max-width: 90%;
            }
          }
        }
      }

      &.received-message {
        justify-content: flex-start;

        .message-bubble {
          max-width: 75%;
          margin-right: auto;
          margin-left: 12px; // Space for arrow with border
          @media (max-width: 575px) {
            max-width: 90%;
          }
        }

        &.internal-note-wrapper {
          justify-content: flex-start;

          .message-bubble {
            max-width: 80%;
            margin-right: auto;
            margin-left: 12px; // Space for arrow with border
            @media (max-width: 575px) {
              max-width: 90%;
            }
          }
        }
      }

      .message-bubble {
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
        background-color: var(--color-white);
        border: 1px solid var(--border-color-light-gray);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        position: relative;
        transition: box-shadow 0.2s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        &.sent-bubble {
          background-color: var(--color-primary);
          border-color: var(--color-primary);
          color: var(--color-white);

          .message-header .sender-name,
          .message-content .message-text {
            color: var(--color-white);
          }

          .message-header .message-timestamp {
            color: rgba(255, 255, 255, 0.8);
          }

          // Chat bubble tail for sent messages
          &::after {
            content: '';
            position: absolute;
            top: 15px;
            right: -10px;
            width: 0;
            height: 0;
            border-left: 10px solid var(--color-primary);
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            z-index: 2;
          }

          // Add border for sent message arrow
          &::before {
            content: '';
            position: absolute;
            top: 14px;
            right: -11px;
            width: 0;
            height: 0;
            border-left: 11px solid var(--border-color-light-gray);
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            z-index: 1;
          }
        }

        &.received-bubble {
          background-color: var(--color-white);
          border-color: var(--border-color-light-gray);

          // Chat bubble tail for received messages
          &::after {
            content: '';
            position: absolute;
            top: 15px;
            left: -10px;
            width: 0;
            height: 0;
            border-right: 10px solid var(--color-white);
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            z-index: 2;
          }

          // Add border for received message arrow
          &::before {
            content: '';
            position: absolute;
            top: 14px;
            left: -11px;
            width: 0;
            height: 0;
            border-right: 11px solid var(--border-color-light-gray);
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            z-index: 1;
          }
        }

        &.internal-bubble {
          background-color: var(--color-light-champagne);
          border-color: var(--color-warning);

          // Override text colors for internal notes
          .message-header .sender-name,
          .message-content .message-text {
            color: var(--color-dark) !important;
          }

          .message-header .message-timestamp {
            color: var(--color-dark-50) !important;
          }

          // Keep chat bubble tails for internal notes
          &::after,
          &::before {
            display: block;
          }

          // Update tail colors for internal notes
          &.sent-bubble::after {
            border-left: 10px solid var(--color-light-champagne);
            right: -10px;
          }

          &.sent-bubble::before {
            border-left: 11px solid var(--color-warning);
            right: -11px;
          }

          &.received-bubble::after {
            border-right: 10px solid var(--color-light-champagne);
            left: -10px;
          }

          &.received-bubble::before {
            border-right: 11px solid var(--color-warning);
            left: -11px;
          }
        }

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-sm);
          gap: var(--spacing-sm);

          .sender-name {
            font-weight: var(--font-weight-medium);
            color: var(--text-color-black);
            font-size: var(--font-size-sm);
            flex: 1;
            min-width: 0; // Allow text to wrap

            .internal-note-badge {
              margin-left: var(--spacing-sm);
              padding: var(--spacing-xs) var(--spacing-sm);
              background-color: var(--color-warning);
              color: var(--color-white);
              border-radius: var(--border-radius-xs);
              text-transform: capitalize;
              font-size: var(--font-size-xs);
              white-space: nowrap;
            }
          }

          .message-timestamp {
            color: var(--color-dark-50);
            font-size: var(--font-size-xs);
            white-space: nowrap;
            flex-shrink: 0;
            margin-left: auto;
          }
        }

        .message-content {
          .message-text {
            color: var(--color-dark);
            line-height: var(--line-height-relaxed);
            word-wrap: break-word;
            white-space: pre-wrap;
          }

          // Change Request attachment styles
          .message-attachments {
            margin-top: var(--spacing-sm);

            .file-grid-container {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              column-gap: var(--spacing-xl);
              row-gap: var(--spacing-xs);

              .file-grid-item {
                border: var(--normal-sec-border);
                border-radius: var(--border-radius-sm);
                padding: var(--spacing-sm);
              }

              @media (max-width: 991px) {
                grid-template-columns: repeat(2, 1fr);
              }

              @media (max-width: 575px) {
                grid-template-columns: repeat(1, 1fr);
              }
            }

            .selected-files {
              display: flex;
              justify-content: space-between;
              align-items: center;
              max-width: 640px;
              margin-bottom: var(--spacing-sm);

              svg {
                width: 24px;
                height: 24px;
                cursor: pointer;
              }

              .file-name {
                width: calc(100% - 10px - 24px);

                p {
                  word-break: break-all;
                }
              }
            }

            .selected-view-files {
              margin-bottom: var(--spacing-xxs);

              svg {
                width: 20px;
                height: 20px;
                cursor: pointer;
              }
            }
          }
        }
      }
    }

    // Loading indicator styling
    .load-more-container {
      padding: var(--spacing-md);
      background: transparent;
    }
    @media (max-width: 575px) {
      padding: var(--spacing-base) var(--spacing-xxs);
    }
  }

  // Animation keyframes
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .internal-note-checkbox {
    background-color: var(--color-white);
    margin: var(--spacing-xs) var(--spacing-lg);
    .visibility-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
      transition: color 0.2s ease;

      &.active {
        color: var(--color-primary);
      }
    }

    .checkbox-label {
      color: var(--color-dark-50);
      margin: 0;
    }
  }

  .message-input-container {
    background-color: var(--color-white);
    border-top: 1px solid var(--border-color-light-gray);
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg);
  }

  .conversation-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: var(--color-light-gray-bg, #f8f9fa);
    border: 1px solid var(--border-color-light-gray);
    border-radius: 25px;
    margin: var(--spacing-none) var(--spacing-lg) var(--spacing-lg);
    transition:
      border-color 0.2s ease,
      box-shadow 0.2s ease;

    &:focus-within {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }

    .conversation-input-container {
      flex: 1;

      .conversation-input {
        background-color: transparent;
        border: none;
        padding: var(--spacing-xs);
        font-size: var(--font-size-sm);

        &::placeholder {
          color: var(--color-dark-50);
          font-size: var(--font-size-sm);
        }

        .MuiOutlinedInput-root {
          border: none;

          &:hover .MuiOutlinedInput-notchedOutline,
          &.Mui-focused .MuiOutlinedInput-notchedOutline {
            border: none;
          }
        }

        .MuiOutlinedInput-notchedOutline {
          border: none;
        }
      }
    }

    .conversation-send-button-icon {
      min-width: 40px;
      max-width: 40px;
      min-height: 40px;
      max-height: 40px;
      border-radius: 50%;
      background-color: var(--color-primary);
      border: none;
      color: var(--color-white);
      transition: all 0.2s ease;
      margin-left: var(--spacing-xs);

      &:hover:not(:disabled) {
        background-color: var(--color-dark-blue);
        transform: scale(1.05);
      }

      &:disabled {
        background-color: var(--color-dark-20);
        cursor: not-allowed;
      }

      .MuiButton-startIcon {
        margin: 0;
      }

      svg {
        font-size: 18px;
      }
    }
  }
}
