import React, { useState, useEffect, useRef } from 'react';
import { EmptyConversationIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import {
  DateFormat,
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { supportTicketService } from '@/services/supportTicketService';
import SendIcon from '@mui/icons-material/Send';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DownloadIcon from '@mui/icons-material/Download';
import HeaderImage from '@/components/UI/ImageSecurity';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import './conversation.scss';

export default function Conversation({ ticketId, ticket, isFollower = false }) {
  // Dynamic height calculation for conversation container
  useEffect(() => {
    const updateConversationHeight = () => {
      // Only calculate height when conversation component is mounted and visible
      const conversationContainer = document.querySelector(
        '.conversation-container'
      );
      if (!conversationContainer) return;

      const header = document.getElementById('site-header');
      const ticketHeader = document.querySelector('.ticket-header');
      const tabsContainer = document.querySelector('.ticket-tabs');
      const ticketInfo = document.querySelector('.ticket-info-section');

      let totalOffset = 176; // Default fallback

      if (header) {
        totalOffset = header.offsetHeight;
      }

      if (ticketHeader) {
        totalOffset += ticketHeader.offsetHeight;
      }

      if (tabsContainer) {
        totalOffset += tabsContainer.offsetHeight;
      }

      if (ticketInfo) {
        totalOffset += ticketInfo.offsetHeight;
      }

      // Add some padding for better UX and account for margins
      totalOffset += 60;

      document.documentElement.style.setProperty(
        '--conversation-height',
        `calc(100vh - ${totalOffset}px)`
      );
    };

    // Initial calculation
    updateConversationHeight();

    // Update on window resize
    window.addEventListener('resize', updateConversationHeight);

    // Update when DOM changes (for dynamic content)
    const observer = new MutationObserver(() => {
      // Debounce the updates to avoid excessive calculations
      setTimeout(updateConversationHeight, 100);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style'],
    });

    return () => {
      window.removeEventListener('resize', updateConversationHeight);
      observer.disconnect();
      // Reset the CSS variable when component unmounts
      document.documentElement.style.removeProperty('--conversation-height');
    };
  }, []);
  // Check user roles first
  const isSuperAdmin = checkOrganizationRole('super_admin');
  const isOrgMaster = checkOrganizationRole('org_master');

  const [newMessage, setNewMessage] = useState('');
  const [isInternalNote, setIsInternalNote] = useState(isSuperAdmin); // Default to true for super_admin, false for others
  const [conversationData, setConversationData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const conversationRef = useRef(null);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  const [previousScrollHeight, setPreviousScrollHeight] = useState(0);

  // Get current user ID from storage
  const getCurrentUserId = () => {
    const authData = fetchFromStorage(identifiers.AUTH_DATA);
    return authData?.user?.id || authData?.id || authData?.user_id;
  };

  // Use isAgentOrAdmin from ticket response instead of environment organization matching
  const isAgentOrAdmin = ticket?.isAgentOrAdmin || false;

  // Check if current user is the assigned user
  const currentUserId = getCurrentUserId();
  const isAssignedUser = ticket?.assigned_to_user_id === currentUserId;

  // Common message input component to avoid all duplication
  const renderMessageInput = () => {
    // Don't render message input if user is a follower (view-only mode)
    if (isFollower) {
      return null;
    }

    return (
      <>
        {/* Internal note checkbox - visible for super admin and assigned users */}
        {(isSuperAdmin || isAssignedUser) && (
          <Box className="internal-note-checkbox d-flex align-center">
            <CustomCheckbox
              checked={isInternalNote || false} // Explicitly ensure it's never undefined
              onChange={(e) => setIsInternalNote(e?.target?.checked || false)}
              name="internal_note"
              defaultChecked={false} // Explicitly set default to false
            />
            <Box className="d-flex align-center gap-sm">
              {isInternalNote ? (
                <VisibilityIcon className="visibility-icon active" />
              ) : (
                <VisibilityOffIcon className="visibility-icon" />
              )}
              <Typography className="checkbox-label body-sm">
                Internal note (visible to agents only)
              </Typography>
            </Box>
          </Box>
        )}

        {/* Message input container */}
        <Box className="message-input-container">
          <Box className="conversation-input-wrapper d-flex align-center gap-sm">
            <Box className="conversation-input-container">
              <CustomTextField
                className="conversation-input"
                fullWidth
                placeholder="Type something..."
                value={newMessage}
                onChange={(e) => setNewMessage(e?.target?.value)}
                onKeyDown={handleKeyDown}
                variant="outlined"
                maxRows={1}
                minRows={1}
                multiline={false}
                disabled={sendingMessage}
              />
            </Box>
            <Box
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (!newMessage?.trim() || sendingMessage) return;
                handleSendMessage(e);
              }}
              style={{
                cursor:
                  !newMessage?.trim() || sendingMessage
                    ? 'not-allowed'
                    : 'pointer',
                opacity: !newMessage?.trim() || sendingMessage ? 0.5 : 1,
              }}
            >
              <CustomButton
                type="button"
                startIcon={<SendIcon />}
                isIconOnly
                disabled={!newMessage?.trim() || sendingMessage}
                className="conversation-send-button-icon"
                variant="outlined"
                style={{ pointerEvents: 'none' }}
              />
            </Box>
          </Box>
        </Box>
      </>
    );
  };

  // Smart function to determine sender display name based on message data and user roles
  const getSenderDisplayName = (message) => {
    const currentUserId = getCurrentUserId();
    const messageCreatedBy = message?.created_by;
    const messageType = message?.message_type;
    const senderName = message?.sender_name;

    // If current user sent this message, always show "You"
    if (messageCreatedBy === currentUserId) {
      return 'You';
    }

    // For super_admin viewing messages
    if (isSuperAdmin) {
      // Super admin sees actual sender names for all messages
      return senderName || 'User';
    }

    // For org_master viewing messages
    if (isOrgMaster) {
      // If message is from org_master themselves, show "You" (handled above)
      // If message is from super_admin or isAgentOrAdmin, show "Support Team"
      if (messageType === 'AGENT') {
        return 'Support Team';
      }
      // If message is from assigned user or other users, show "Support Team"
      return 'Support Team';
    }

    // For isAgentOrAdmin users viewing messages
    if (isAgentOrAdmin) {
      // If they sent the message, show "You" (handled above)
      // If message is from org_master, show sender name
      if (messageType === 'USER' && messageCreatedBy === ticket?.creator_id) {
        return senderName || 'User';
      }
      // If message is from super_admin, show sender name
      if (messageType === 'AGENT') {
        return senderName || 'Support Team';
      }
      // For other messages, show sender name
      return senderName || 'User';
    }

    // Fallback for other roles
    return senderName || 'User';
  };

  // Set internal note checkbox default based on user role on component mount and ticket changes
  useEffect(() => {
    setIsInternalNote(isSuperAdmin); // Default to true for super_admin, false for others
  }, [isSuperAdmin]);

  // Fetch conversation data when ticketId changes
  useEffect(() => {
    if (ticketId) {
      fetchConversationData(1, true);
      if (isSuperAdmin) {
        setIsInternalNote(true);
      } else {
        setIsInternalNote(false);
      }
    }
  }, [ticketId]);

  // Add scroll event listener for infinite scroll
  useEffect(() => {
    const conversationContainer = conversationRef.current;
    if (conversationContainer) {
      conversationContainer.addEventListener('scroll', handleScroll);
      return () => {
        conversationContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [hasMore, loading, currentPage]); // Dependencies for the scroll handler

  // Check if we need to load more data when content doesn't fill the container
  useEffect(() => {
    const conversationContainer = conversationRef.current;
    if (conversationContainer && !loading && hasMore && currentPage === 1) {
      const { scrollHeight, clientHeight } = conversationContainer;
      // If content doesn't fill the container, load more
      if (scrollHeight <= clientHeight) {
        loadMoreMessages();
      }
    }
  }, [conversationData, hasMore, loading, currentPage]);

  // Scroll to bottom when new messages are added or on initial load
  useEffect(() => {
    const conversationContainer = conversationRef.current;
    if (conversationContainer && shouldScrollToBottom) {
      conversationContainer.scrollTop = conversationContainer.scrollHeight;
      setShouldScrollToBottom(false);
    }
  }, [conversationData, shouldScrollToBottom]);

  // Maintain scroll position when loading older messages
  useEffect(() => {
    const conversationContainer = conversationRef.current;
    if (
      conversationContainer &&
      previousScrollHeight > 0 &&
      !shouldScrollToBottom
    ) {
      // Calculate the difference in scroll height and adjust scroll position
      const newScrollHeight = conversationContainer.scrollHeight;
      const scrollDifference = newScrollHeight - previousScrollHeight;
      conversationContainer.scrollTop = scrollDifference;
      setPreviousScrollHeight(0); // Reset after adjusting
    }
  }, [conversationData, previousScrollHeight, shouldScrollToBottom]);

  // Function to load more messages
  const loadMoreMessages = () => {
    if (hasMore && !loading) {
      fetchConversationData(currentPage + 1, false);
    }
  };

  // Scroll handler for infinite scroll
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e?.target || {};

    // For chat interfaces, load more when scrolling near the top (to load older messages)
    // Load more when user scrolls to within 50px of the top
    if (scrollTop <= 50 && hasMore && !loading) {
      loadMoreMessages();
    }

    // Also check if container is not scrollable but has more data
    // This handles cases where initial content doesn't fill the container
    if (
      scrollHeight <= clientHeight &&
      hasMore &&
      !loading &&
      currentPage === 1
    ) {
      loadMoreMessages();
    }
  };

  const fetchConversationData = async (page = 1, reset = false) => {
    if (!ticketId) return;

    setLoading(true);
    try {
      // Logic for include_private:
      // - super_admin: true (can see internal notes)
      // - assigned user: true (can see internal notes)
      // - org_master: false (cannot see internal notes)
      // - staff: false (cannot see internal notes)
      // - followers: false (cannot see internal notes, but can see regular messages)
      const shouldIncludePrivate = isSuperAdmin || isAssignedUser;

      const params = {
        include_private: shouldIncludePrivate,
        page: page,
        limit: 10,
      };

      const response = await supportTicketService.getTicketConversation(
        ticketId,
        params
      );

      // Handle nested data structure: response.data.data
      const messagesData = response?.data?.data || response?.data || [];
      const paginationData =
        response?.data?.pagination || response?.pagination || {};

      if (messagesData && Array.isArray(messagesData)) {
        if (reset || page === 1) {
          // For first page, reverse the order to show oldest first (chronological order)
          // API returns newest first, but we want oldest first for chat display
          const reversedMessages = [...messagesData].reverse();
          setConversationData(reversedMessages);
          setShouldScrollToBottom(true); // Scroll to bottom on initial load
        } else {
          // For subsequent pages (loading older messages), reverse and prepend
          // API gives newer messages first on each page, so reverse them and prepend
          const reversedMessages = [...messagesData].reverse();

          // Store current scroll height before adding new messages
          const conversationContainer = conversationRef.current;
          if (conversationContainer) {
            setPreviousScrollHeight(conversationContainer.scrollHeight);
          }

          setConversationData((prev) => [...reversedMessages, ...(prev || [])]);
        }

        setCurrentPage(page);
        setHasMore(paginationData?.hasNextPage || false);
      } else {
        if (reset || page === 1) {
          setConversationData([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching conversation:', error);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch conversation'
      );
      if (reset || page === 1) {
        setConversationData([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Filter out internal notes based on user role:
  // - super_admin can see ALL internal notes
  // - assigned user can see internal notes from super_admin AND their own internal notes
  // - org_master cannot see internal notes at all
  // - followers cannot see internal notes at all (but can see regular messages)
  const filteredConversationData =
    conversationData?.filter((message) => {
      // If it's not an internal note, show it to everyone
      if (message?.is_private !== 1) {
        return true;
      }

      // If it's an internal note:
      // - super_admin can see all internal notes
      if (isSuperAdmin) {
        return true;
      }

      // - assigned user can see internal notes from super_admin AND their own internal notes
      if (isAssignedUser) {
        // Show internal notes created by super_admin or by the assigned user themselves
        const messageCreatedBy = message?.created_by;
        const isFromSuperAdmin = message?.message_type === 'AGENT'; // Internal notes from super_admin are AGENT type
        const isOwnMessage = messageCreatedBy === currentUserId;

        return isFromSuperAdmin || isOwnMessage;
      }

      // - All other users (including org_master and followers) cannot see internal notes
      return false;
    }) || [];

  const handleSendMessage = async (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!newMessage?.trim() || !ticketId || sendingMessage) return;

    const messageText = newMessage?.trim();
    setNewMessage(''); // Clear input immediately for better UX
    setSendingMessage(true);

    try {
      // Determine message type: AGENT if super admin or assigned user
      const shouldBeAgent = isSuperAdmin || isAssignedUser;

      const messageData = {
        message_text: messageText,
        is_private: isInternalNote,
        message_type: shouldBeAgent ? 'AGENT' : 'USER',
      };

      const response = await supportTicketService.addTicketMessage(
        ticketId,
        messageData
      );

      if (response) {
        // Add the new message immediately to show it right away
        if (response?.data) {
          // Ensure the new message has all required properties for proper display
          const newMessage = {
            ...response?.data,
            is_private: isInternalNote ? 1 : 0, // Ensure is_private is set correctly
            message_type: shouldBeAgent ? 'AGENT' : 'USER',
            created_by: getCurrentUserId(),
          };
          setConversationData((prev) => [...(prev || []), newMessage]);
          setShouldScrollToBottom(true); // Scroll to bottom after sending message
        }

        // Reset internal note checkbox after sending message - keep it selected for super_admin
        setIsInternalNote(isSuperAdmin);

        // Show the success message from API response
        setApiMessage('success', response?.message);

        // No need to refresh conversation data - we already added the new message above
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setApiMessage('error', error?.response?.data?.message);
      // Restore the message text if sending failed
      setNewMessage(messageText);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e?.key === 'Enter' && !e?.shiftKey && !sendingMessage) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  // Handle download for attachments
  const handleDownload = async (fileUrl, fileName) => {
    try {
      if (!fileUrl || !fileName) {
        console.error('Invalid file URL or filename');
        return;
      }

      if (fileUrl.startsWith('http')) {
        const response = await fetch(fileUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);
      } else {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Download failed:', error);
      window.open(fileUrl, '_blank');
    }
  };

  // Render attachments using Change Request UI style
  const renderAttachments = (attachments) => {
    if (!attachments || attachments.length === 0) return null;

    return (
      <Box className="message-attachments pt8">
        <Typography className="body-text fw500 pb4">Attached files</Typography>
        <Box className="file-grid-container">
          {attachments.map((attachment, index) => {
            const fileName =
              attachment?.attachment_name ||
              attachment?.item_name ||
              attachment?.name ||
              'Unknown file';
            const fileUrl =
              attachment?.download_url ||
              attachment?.url ||
              attachment?.preview;
            const mimeType =
              attachment?.mime_type ||
              attachment?.item_mime_type ||
              attachment?.type ||
              '';
            const isImage = mimeType.startsWith('image/');

            return (
              <Box
                key={index}
                className="selected-files selected-view-files file-grid-item"
              >
                <Box className="file-name">
                  <Box className="d-flex align-center gap-sm">
                    <InsertDriveFileIcon className="file-icon" />
                    <Typography className="title-text text-ellipsis-line text-capital">
                      {fileName}
                    </Typography>
                  </Box>
                </Box>
                {isImage && fileUrl && (
                  <HeaderImage
                    type="url"
                    imageUrl={fileUrl}
                    Content={<RemoveRedEyeIcon />}
                    className="d-flex align-center"
                  />
                )}
                <DownloadIcon
                  className="ml8"
                  onClick={() => handleDownload(fileUrl, fileName)}
                  sx={{ cursor: 'pointer' }}
                />
              </Box>
            );
          })}
        </Box>
      </Box>
    );
  };

  if (loading) {
    return (
      <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
        <ContentLoader />
      </Box>
    );
  }

  if (!filteredConversationData?.length) {
    return (
      <Box className="conversation-container">
        <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
          <EmptyConversationIcon className="conversation-icon" />
          <Box>
            <Typography className="conversation-text body-sm">
              No Conversation available
            </Typography>
          </Box>
        </Box>
        {renderMessageInput()}
      </Box>
    );
  }

  return (
    <Box className="conversation-container">
      <Box className="conversation-messages" ref={conversationRef}>
        {filteredConversationData?.map((message) => {
          const currentUserId = getCurrentUserId();
          const isCurrentUser = message?.created_by === currentUserId;
          const isInternalNote = message?.is_private === 1;

          return (
            <Box
              key={message?.id}
              className={`message-wrapper ${
                isInternalNote
                  ? isCurrentUser
                    ? 'sent-message internal-note-wrapper'
                    : 'received-message internal-note-wrapper'
                  : isCurrentUser
                    ? 'sent-message'
                    : 'received-message'
              }`}
            >
              <Box
                className={`message-bubble ${isCurrentUser ? 'sent-bubble' : 'received-bubble'} ${isInternalNote ? 'internal-bubble' : ''}`}
              >
                <Box className="message-header">
                  <Typography className="sender-name text-capital body-sm">
                    {getSenderDisplayName(message)}
                    {message?.is_private === 1 && (
                      <span className="internal-note-badge sub-title-text">
                        Internal Note
                      </span>
                    )}
                  </Typography>
                  <Typography className="message-timestamp body-xs">
                    {DateFormat(message?.created_at, 'datesWithhour')}
                  </Typography>
                </Box>
                <Box className="message-content">
                  <Typography className="message-text body-sm">
                    {message?.message_text}
                  </Typography>

                  {/* Render attachments using Change Request UI */}
                  {renderAttachments(
                    message?.attachments || message?.files || []
                  )}
                </Box>
              </Box>
            </Box>
          );
        })}

        {/* Loading indicator for infinite scroll */}
        {loading && hasMore && (
          <Box className="load-more-container d-flex justify-center mt16">
            <ContentLoader />
          </Box>
        )}
      </Box>
      {renderMessageInput()}
    </Box>
  );
}
